# TMDB API密钥保存问题修复指南

## 问题描述
在开发服务器环境中，配置的TMDB API密钥无法正常保存到本地data数据文件夹。

## 问题分析
经过详细调试，发现配置保存功能本身是正常的。问题可能出现在以下几个方面：

### 1. 开发服务器未启动
**症状**: 前端无法调用API端点保存配置
**解决方案**: 
```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```
确保服务器在 `http://localhost:3000` 正常运行。

### 2. API密钥格式不正确
**症状**: 保存时提示"API密钥格式不正确"
**解决方案**: 
- TMDB API密钥必须是32位十六进制字符串
- 只能包含字符：0-9, a-f, A-F
- 示例正确格式：`abcdef1234567890abcdef1234567890`

### 3. 浏览器缓存问题
**症状**: 配置保存后页面显示未更新
**解决方案**: 
- Windows: 按 `Ctrl+Shift+R` 强制刷新
- Mac: 按 `Cmd+Shift+R` 强制刷新
- 或打开开发者工具，右键刷新按钮选择"清空缓存并硬性重新加载"

### 4. 文件权限问题
**症状**: 保存时出现权限错误
**解决方案**: 
- Windows: 右键data文件夹 → 属性 → 安全 → 确保当前用户有完全控制权限
- Linux/Mac: `chmod 755 data/`

## 修复内容

### 1. 增强了错误处理
- 在 `lib/docker-config-manager.ts` 中添加了更详细的错误信息
- 在 `app/api/docker-config/route.ts` 中增强了API密钥验证和错误处理

### 2. 添加了配置备份机制
- 保存新配置前会自动备份现有配置
- 防止配置文件损坏导致的数据丢失

### 3. 增强了调试信息
- 添加了详细的控制台日志输出
- 便于用户和开发者诊断问题

## 使用诊断工具

我们提供了一个诊断工具来帮助检查配置：

```bash
node diagnose-tmdb-config.js
```

该工具会检查：
- 项目结构完整性
- data目录权限
- 环境配置
- 配置保存功能

## 手动验证步骤

### 1. 检查配置文件
```bash
# 查看配置文件是否存在
ls -la data/app-config.json

# 查看配置文件内容
cat data/app-config.json
```

### 2. 测试配置保存
```bash
# 运行测试脚本
node test-tmdb-config.js
```

### 3. 检查API端点
在浏览器开发者工具中：
1. 打开Network标签
2. 尝试保存TMDB API密钥
3. 查看是否有API请求发出
4. 检查请求和响应的状态

## 常见错误及解决方案

### 错误1: "配置保存失败"
**原因**: 文件系统权限问题
**解决**: 检查data目录权限，确保可写

### 错误2: "API密钥格式不正确"
**原因**: API密钥不是32位十六进制格式
**解决**: 从TMDB官网重新获取正确格式的API密钥

### 错误3: "此API只能在服务器端调用"
**原因**: 开发服务器未启动或API路由有问题
**解决**: 重启开发服务器，检查路由配置

### 错误4: 配置保存后不生效
**原因**: 浏览器缓存或前端状态管理问题
**解决**: 清除浏览器缓存，刷新页面

## 验证修复结果

1. **启动开发服务器**:
   ```bash
   npm run dev
   ```

2. **打开设置页面**: 访问 `http://localhost:3000` 并进入设置

3. **输入TMDB API密钥**: 确保是32位十六进制格式

4. **保存配置**: 点击保存按钮

5. **检查结果**: 
   - 查看浏览器控制台是否有错误
   - 检查 `data/app-config.json` 文件是否更新
   - 刷新页面确认配置已保存

## 技术细节

### 环境检测逻辑
```javascript
// 环境类型判断
if (typeof window !== 'undefined') {
    return 'client';  // 浏览器环境
}
if (process.env.DOCKER_CONTAINER === 'true' || fs.existsSync('/.dockerenv')) {
    return 'docker';  // Docker环境
}
return 'development';  // 开发环境
```

### 配置文件路径
- 开发环境: `{项目根目录}/data/app-config.json`
- Docker环境: `/app/data/app-config.json`

### API端点
- GET `/api/docker-config`: 获取配置
- POST `/api/docker-config`: 保存配置

## 联系支持

如果按照本指南操作后仍然遇到问题，请提供以下信息：
1. 操作系统版本
2. Node.js版本
3. 浏览器控制台错误信息
4. 诊断工具的输出结果
5. 具体的操作步骤和错误现象
